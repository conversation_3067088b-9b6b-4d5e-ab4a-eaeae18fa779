{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-69:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6806dc609573cce0b061254c5042724b\\transformed\\play-services-base-18.0.1\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1250,1357,1533,1671,1780,1938,2074,2196,2454,2633,2740,2918,3056,3218,3397,3465,3531", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "1352,1528,1666,1775,1933,2069,2191,2304,2628,2735,2913,3051,3213,3392,3460,3526,3607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\07f408d290f2498181cf432fde42d716\\transformed\\browser-1.4.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3612,4004,4109,4220", "endColumns": "102,104,110,104", "endOffsets": "3710,4104,4215,4320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\364994c224617bb5658cf91cfb556286\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11967,12053", "endColumns": "85,91", "endOffsets": "12048,12140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e9549621c83de7ce5d44746d266914d0\\transformed\\credentials-1.2.0-rc01\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,113", "endOffsets": "212,326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0062a5e1d214efb62fafece10c34c46\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "331,428,530,631,728,835,943,11595", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "423,525,626,723,830,938,1060,11691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4106571b134d49d860c08c6ca142a08e\\transformed\\play-services-basement-18.4.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2309", "endColumns": "144", "endOffsets": "2449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\92faffc5ee3d50988e6ae9072fb227ba\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,1016,1102,1173,1256,1333,1408,1486,1552", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,1011,1097,1168,1251,1328,1403,1481,1547,1674"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1065,1164,3715,3812,3914,4325,4407,10940,11032,11116,11203,11289,11360,11443,11520,11696,11774,11840", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "1159,1245,3807,3909,3999,4402,4494,11027,11111,11198,11284,11355,11438,11515,11590,11769,11835,11962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8aaeb18dd8abb7cc16bdd65ee58c7a94\\transformed\\material3-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4821,4910,4999,5106,5186,5270,5370,5474,5574,5680,5768,5880,5985,6095,6214,6294,6401", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4816,4905,4994,5101,5181,5265,5365,5469,5569,5675,5763,5875,5980,6090,6209,6289,6396,6491"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4499,4623,4745,4863,4984,5083,5183,5300,5447,5574,5724,5809,5908,6003,6101,6222,6360,6464,6611,6759,6906,7076,7214,7337,7462,7587,7683,7782,7907,8042,8149,8253,8366,8511,8660,8776,8882,8958,9058,9155,9265,9354,9443,9550,9630,9714,9814,9918,10018,10124,10212,10324,10429,10539,10658,10738,10845", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "4618,4740,4858,4979,5078,5178,5295,5442,5569,5719,5804,5903,5998,6096,6217,6355,6459,6606,6754,6901,7071,7209,7332,7457,7582,7678,7777,7902,8037,8144,8248,8361,8506,8655,8771,8877,8953,9053,9150,9260,9349,9438,9545,9625,9709,9809,9913,10013,10119,10207,10319,10424,10534,10653,10733,10840,10935"}}]}]}