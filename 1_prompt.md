# 🐞 Prompt: Fix App Crash When Saving Habits with Weekly Frequency

## 1. Objective

To fix a critical app crash (`java.lang.IllegalArgumentException`) that occurs when a user tries to save a new habit with a custom weekly frequency (e.g., repeating on specific days of the week).

## 2. Visual Reference & Root Cause Analysis

The exact error is captured in the stack trace provided in **`38.jpg`**.

The key line in the error message is: `java.lang.IllegalArgumentException: Invalid format: "[Ljava.lang.String;@..." is malformed`.

This tells us the exact problem:
* The `daysOfWeek` property, which should be a list of numbers (e.g., `[1, 4, 6]` for Mon, Thu, Sat), is being saved to Firestore as a raw Java array's memory address string (e.g., `[Ljava.lang.String;@...`).
* Firestore's backend does not know how to parse this string and throws an exception, causing the app to crash.
* The error originates in the repository layer when the `Habit` object is being sent to Firestore for creation. The data is not in a Firestore-compatible format.

## 3. Detailed Implementation Plan

The fix involves ensuring that the `Habit` data object is correctly formatted before it's sent to the Firestore SDK.

* **Task 3.1: Locate the Faulty Code**
    * Navigate to the `HabitRepository` file where the function to create/insert a new habit is located.
    * Find the line of code that calls the Firestore `add()` or `set()` method to save the habit object.

* **Task 3.2: Ensure Correct Data Serialization**
    * Before the `add()` or `set()` call, you must ensure that the `Habit` object being passed is serializable by Firestore. The `daysOfWeek` list is the specific problem.
    * The `daysOfWeek` property of the habit object must be a proper `List<Int>` or `List<String>`. It cannot be a raw Array object whose `.toString()` method is being called implicitly.
    * **Solution:** Before saving, explicitly convert the `Habit` data class into a `Map<String, Any>`. In this map, ensure the value for the "daysOfWeek" key is the actual `List` object, not a string representation of an array. This guarantees that the Firestore SDK will correctly serialize it as a Firestore Array.

    ```kotlin
    // Example of how to structure the data before saving
    val habitData = mapOf(
        "name" to habit.name,
        "frequencyType" to habit.frequencyType.name,
        // ... other habit properties
        "daysOfWeek" to habit.daysOfWeek // This must be a clean List<Int> or List<String>
    )
    firestore.collection("...").add(habitData)
    ```

## 4. Verification / Testing Section

* **Test Case 1: Fix the Crash**
    * Follow the exact steps from the bug report: create a new "Yes/No" habit with a weekly frequency set for Monday, Thursday, and Saturday.
    * Click the "Save" button.
    * **Expected Outcome:** The app must **not** crash. It should save the habit and navigate back to the home screen.

* **Test Case 2: Verify Correct Data in Firestore**
    * After successfully saving the habit, go to your Firebase Console and open the Firestore database.
    * Navigate to the newly created habit document inside the `users/{userId}/habits` collection.
    * **Expected Outcome:** Inspect the `daysOfWeek` field. It must be stored as a Firestore **Array** containing the selected days (e.g., `[1, 4, 6]`). It must **not** be a string.

## 5. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.


# Debug Process Guide

Please follow this exact structured debugging process to investigate and resolve the issue:


## 1. Understand the Error

- thoroughly understand the error context!
- Carefully interpret what the error says:
  - Understand the type of exception
  - Analyze the stack trace
  - Note any file paths or line numbers referenced

## 2. Trace the Error Location

- Navigate to all relevant files and lines mentioned in the error log
- Understand the surrounding code and the full implementation of the feature where the error occurred
- Focus on:
  - Control flow
  - Input/output
  - Any dependent components

## 3. Comprehend the Current Feature Implementation

- Develop a clear mental model of how the entire feature is structured and functions
- Identify how the involved files and methods interact
- Understand what they're intended to do

## 4. Determine the Root Cause

> **Important**: Before implementing any changes, it is mandatory to identify and finalize the true root cause of the issue.

Think deeply about potential causes:
- Logic error
- Missing configuration
- Incompatible encoding
- Race condition
- Misused library

**Clearly state the root cause once identified.**

## 5. Cross-Reference the Reference Project

- Once the root cause is finalized, examine the reference project at `./uhabits-dev`
- Compare relevant parts of the implementation
- Look for differences or proven approaches that could guide the solution

## 6. Plan and Execute the Fix

After gaining full understanding and validating it against the reference project, proceed to implement the fix with precision.

Ensure that:
1. The change is minimal and localized
2. It addresses the root cause directly
3. It does not introduce side effects

## 7. Verify

Test the fix thoroughly to ensure the issue is resolved.
