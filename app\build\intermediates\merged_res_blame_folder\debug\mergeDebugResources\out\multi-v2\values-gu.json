{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-69:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6806dc609573cce0b061254c5042724b\\transformed\\play-services-base-18.0.1\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1224,1332,1498,1623,1735,1873,1995,2106,2353,2501,2609,2773,2898,3041,3191,3252,3318", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "1327,1493,1618,1730,1868,1990,2101,2201,2496,2604,2768,2893,3036,3186,3247,3313,3395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e9549621c83de7ce5d44746d266914d0\\transformed\\credentials-1.2.0-rc01\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,216", "endColumns": "110,111", "endOffsets": "211,323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0062a5e1d214efb62fafece10c34c46\\transformed\\core-1.16.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,353,455,557,655,777", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "148,251,348,450,552,650,772,873"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "328,426,529,626,728,830,928,11155", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "421,524,621,723,825,923,1045,11251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\07f408d290f2498181cf432fde42d716\\transformed\\browser-1.4.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3400,3780,3881,3990", "endColumns": "100,100,108,100", "endOffsets": "3496,3876,3985,4086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4106571b134d49d860c08c6ca142a08e\\transformed\\play-services-basement-18.4.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2206", "endColumns": "146", "endOffsets": "2348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8aaeb18dd8abb7cc16bdd65ee58c7a94\\transformed\\material3-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4671,4756,4843,4941,5021,5105,5205,5308,5406,5506,5593,5699,5798,5901,6019,6099,6199", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4666,4751,4838,4936,5016,5100,5200,5303,5401,5501,5588,5694,5793,5896,6014,6094,6194,6288"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4278,4392,4504,4629,4741,4836,4935,5051,5190,5310,5452,5537,5641,5735,5835,5949,6077,6186,6321,6453,6583,6762,6888,7010,7136,7271,7366,7462,7589,7719,7820,7925,8032,8167,8308,8417,8519,8594,8691,8787,8894,8979,9066,9164,9244,9328,9428,9531,9629,9729,9816,9922,10021,10124,10242,10322,10422", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "4387,4499,4624,4736,4831,4930,5046,5185,5305,5447,5532,5636,5730,5830,5944,6072,6181,6316,6448,6578,6757,6883,7005,7131,7266,7361,7457,7584,7714,7815,7920,8027,8162,8303,8412,8514,8589,8686,8782,8889,8974,9061,9159,9239,9323,9423,9526,9624,9724,9811,9917,10016,10119,10237,10317,10417,10511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\364994c224617bb5658cf91cfb556286\\transformed\\foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11514,11599", "endColumns": "84,84", "endOffsets": "11594,11679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\92faffc5ee3d50988e6ae9072fb227ba\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,1001,1084,1159,1234,1309,1384,1460,1526", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,996,1079,1154,1229,1304,1379,1455,1521,1637"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1050,1142,3501,3594,3693,4091,4177,10516,10603,10689,10772,10855,10930,11005,11080,11256,11332,11398", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "1137,1219,3589,3688,3775,4172,4273,10598,10684,10767,10850,10925,11000,11075,11150,11327,11393,11509"}}]}]}