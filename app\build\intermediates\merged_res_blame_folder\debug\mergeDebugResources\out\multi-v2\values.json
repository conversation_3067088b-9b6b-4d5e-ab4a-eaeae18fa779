{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-69:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f0673b1aecd457c10221017e11ef110b\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7540", "endColumns": "49", "endOffsets": "7585"}}, {"source": "E:\\Habit_Tracker_WorkSpace\\UHabits_99\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "293,57,105,153,201,247,336", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "330,99,147,195,241,287,373"}, "to": {"startLines": "5,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,1595,1642,1689,1736,1781,1826", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,1637,1684,1731,1776,1821,1863"}}, {"source": "E:\\Habit_Tracker_WorkSpace\\UHabits_99\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,136,240,348,468,569", "endColumns": "80,103,107,119,100,69", "endOffsets": "131,235,343,463,564,634"}, "to": {"startLines": "167,168,169,170,171,237", "startColumns": "4,4,4,4,4,4", "startOffsets": "11686,11767,11871,11979,12099,16869", "endColumns": "80,103,107,119,100,69", "endOffsets": "11762,11866,11974,12094,12195,16934"}}, {"source": "E:\\Habit_Tracker_WorkSpace\\UHabits_99\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endColumns": "87", "endOffsets": "142"}, "to": {"startLines": "296", "startColumns": "4", "startOffsets": "20249", "endColumns": "86", "endOffsets": "20331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5e68568aef666c6b33debb00dcf099e5\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "284", "startColumns": "4", "startOffsets": "19616", "endLines": "287", "endColumns": "12", "endOffsets": "19834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\92faffc5ee3d50988e6ae9072fb227ba\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,92,93,125,140,141,161,162,163,172,173,235,236,238,239,240,241,242,243,245,246,247,250,266,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3690,3749,3808,3868,3928,3988,4048,4108,4168,4228,4288,4348,4408,4467,4527,4587,4647,4707,4767,4827,4887,4947,5007,5067,5126,5186,5246,5305,5364,5423,5482,5541,5600,5674,5732,5910,5961,7654,8726,8791,11246,11312,11413,12200,12252,16753,16815,16939,16989,17043,17089,17135,17177,17288,17335,17371,17572,18552,18663", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,92,93,125,140,141,161,162,163,172,173,235,236,238,239,240,241,242,243,245,246,247,252,268,272", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "3744,3803,3863,3923,3983,4043,4103,4163,4223,4283,4343,4403,4462,4522,4582,4642,4702,4762,4822,4882,4942,5002,5062,5121,5181,5241,5300,5359,5418,5477,5536,5595,5669,5727,5782,5956,6011,7702,8786,8840,11307,11408,11466,12247,12307,16810,16864,16984,17038,17084,17130,17172,17212,17330,17366,17456,17679,18658,18853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b6e88755ad5b4c533fcd6b2fa8fb2e3\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "119", "startColumns": "4", "startOffsets": "7340", "endColumns": "42", "endOffsets": "7378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\efd27e88bfa33be47f586616d7b4ba83\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7486", "endColumns": "53", "endOffsets": "7535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2b50d9d4c3fe397e48544049185057d4\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "90,100,124,378,383", "startColumns": "4,4,4,4,4", "startOffsets": "5787,6302,7590,23770,23940", "endLines": "90,100,124,382,386", "endColumns": "56,64,63,24,24", "endOffsets": "5839,6362,7649,23935,24084"}}, {"source": "E:\\Habit_Tracker_WorkSpace\\UHabits_99\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "47", "endOffsets": "60"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "8167", "endColumns": "47", "endOffsets": "8210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a56dc59a73c285a5fb1ee061c400e737\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "99,121", "startColumns": "4,4", "startOffsets": "6260,7426", "endColumns": "41,59", "endOffsets": "6297,7481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd4c28a9cd816fee213d1e3481c2e9cc\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "7383", "endColumns": "42", "endOffsets": "7421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6e71d6d93707fe0d9686d0f9b979770d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "8084", "endColumns": "82", "endOffsets": "8162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4106571b134d49d860c08c6ca142a08e\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "126,150", "startColumns": "4,4", "startOffsets": "7707,9885", "endColumns": "67,166", "endOffsets": "7770,10047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\07f408d290f2498181cf432fde42d716\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "6,7,8,9,28,29,160,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "412,470,536,599,1868,1939,11178,11471,11538,11617", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "465,531,594,656,1934,2006,11241,11533,11612,11681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e9549621c83de7ce5d44746d266914d0\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "129,130", "startColumns": "4,4", "startOffsets": "7918,8000", "endColumns": "81,83", "endOffsets": "7995,8079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9961d480dd52b08a59f7662c2ca5928d\\transformed\\navigation-common-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "439,452,458,464,473", "startColumns": "4,4,4,4,4", "startOffsets": "25253,25892,26136,26383,26746", "endLines": "451,457,463,466,477", "endColumns": "24,24,24,24,24", "endOffsets": "25887,26131,26378,26511,26923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6806dc609573cce0b061254c5042724b\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "12,13,14,15,16,17,18,19,142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159,426,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,882,962,1052,1142,1222,1303,1383,8845,8950,9131,9256,9363,9543,9666,9782,10052,10240,10345,10526,10651,10826,10974,11037,11099,24938,26928", "endLines": "12,13,14,15,16,17,18,19,142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159,438,496", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "877,957,1047,1137,1217,1298,1378,1458,8945,9126,9251,9358,9538,9661,9777,9880,10235,10340,10521,10646,10821,10969,11032,11094,11173,25248,27340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8aaeb18dd8abb7cc16bdd65ee58c7a94\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "127,174,175,176,177,178,179,180,181,182,183,186,187,188,189,190,191,192,193,194,195,196,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,253,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7775,12312,12400,12486,12567,12651,12720,12785,12868,12974,13060,13180,13234,13303,13364,13433,13522,13617,13691,13788,13881,13979,14128,14219,14307,14403,14501,14565,14633,14720,14814,14881,14953,15025,15126,15235,15311,15380,15428,15494,15558,15632,15689,15746,15818,15868,15922,15993,16064,16134,16203,16261,16337,16408,16482,16568,16618,16688,17684,18399", "endLines": "127,174,175,176,177,178,179,180,181,182,185,186,187,188,189,190,191,192,193,194,195,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,262,265", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7843,12395,12481,12562,12646,12715,12780,12863,12969,13055,13175,13229,13298,13359,13428,13517,13612,13686,13783,13876,13974,14123,14214,14302,14398,14496,14560,14628,14715,14809,14876,14948,15020,15121,15230,15306,15375,15423,15489,15553,15627,15684,15741,15813,15863,15917,15988,16059,16129,16198,16256,16332,16403,16477,16563,16613,16683,16748,18394,18547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0062a5e1d214efb62fafece10c34c46\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,95,96,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,133,134,135,136,137,138,139,244,273,274,278,279,283,297,298,306,312,322,357,387,420", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,661,726,1463,1532,2011,2081,2149,2221,2291,2352,2426,2499,2560,2621,2683,2747,2809,2870,2938,3038,3098,3164,3237,3306,3363,3415,3477,3549,3625,6070,6105,6367,6422,6485,6540,6598,6654,6712,6773,6836,6893,6944,7002,7052,7113,7170,7236,7270,7305,7848,8215,8282,8354,8423,8492,8566,8638,17217,18858,18975,19176,19286,19487,20336,20408,20779,20982,21283,23089,24089,24771", "endLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,95,96,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,128,133,134,135,136,137,138,139,244,273,277,278,282,283,297,298,311,321,356,377,419,425", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,721,787,1527,1590,2076,2144,2216,2286,2347,2421,2494,2555,2616,2678,2742,2804,2865,2933,3033,3093,3159,3232,3301,3358,3410,3472,3544,3620,3685,6100,6135,6417,6480,6535,6593,6649,6707,6768,6831,6888,6939,6997,7047,7108,7165,7231,7265,7300,7335,7913,8277,8349,8418,8487,8561,8633,8721,17283,18970,19171,19281,19482,19611,20403,20470,20977,21278,23084,23765,24766,24933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6dd6510939024db6dfff1d92597524bb\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "94,98", "startColumns": "4,4", "startOffsets": "6016,6193", "endColumns": "53,66", "endOffsets": "6065,6255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\94362aa43689fc346a39521984e13d85\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "97,299,467,470", "startColumns": "4,4,4,4", "startOffsets": "6140,20475,26516,26631", "endLines": "97,305,469,472", "endColumns": "52,24,24,24", "endOffsets": "6188,20774,26626,26741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\512de977da2a1ddbca08eb2f7061b9db\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "288", "startColumns": "4", "startOffsets": "19839", "endLines": "295", "endColumns": "8", "endOffsets": "20244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\13d4227fbf750a42b4666692ab5cd43f\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "91", "startColumns": "4", "startOffsets": "5844", "endColumns": "65", "endOffsets": "5905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\364994c224617bb5658cf91cfb556286\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "248,249", "startColumns": "4,4", "startOffsets": "17461,17517", "endColumns": "55,54", "endOffsets": "17512,17567"}}]}]}