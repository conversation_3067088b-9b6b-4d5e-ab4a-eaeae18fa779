package com.example.habits9.ui.auth

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Design System Colors (from style.md)
private val LightBackground = Color(0xFFFFFFFF)
private val LightBackgroundDarker = Color(0xFFF7FAFC)
private val LightTextPrimary = Color(0xFF2D3748)
private val LightTextSecondary = Color(0xFF718096)
private val LightAccentPrimary = Color(0xFF38B2AC)

private val DarkBackground = Color(0xFF121826)
private val DarkBackgroundDarker = Color(0xFF1A202C)
private val DarkTextPrimary = Color(0xFFE2E8F0)
private val DarkTextSecondary = Color(0xFFA0AEC0)
private val DarkAccentPrimary = Color(0xFF81E6D9)

@Composable
fun VerificationScreen(
    email: String,
    onBackToLogin: () -> Unit = {}
) {
    // Use system theme colors but with our design system
    val isDarkTheme = MaterialTheme.colorScheme.background == Color(0xFF121826) || 
                     MaterialTheme.colorScheme.background.red < 0.5f
    
    val backgroundColor = if (isDarkTheme) DarkBackground else LightBackground
    val backgroundDarker = if (isDarkTheme) DarkBackgroundDarker else LightBackgroundDarker
    val textPrimary = if (isDarkTheme) DarkTextPrimary else LightTextPrimary
    val textSecondary = if (isDarkTheme) DarkTextSecondary else LightTextSecondary
    val accentPrimary = if (isDarkTheme) DarkAccentPrimary else LightAccentPrimary
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(backgroundColor, backgroundDarker)
                )
            )
            .padding(horizontal = 20.dp), // viewport spacing from style guide
        contentAlignment = Alignment.Center
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // Email Icon
            Icon(
                imageVector = Icons.Default.Email,
                contentDescription = "Email Verification",
                modifier = Modifier.size(64.dp),
                tint = accentPrimary
            )
            
            // Title
            Text(
                text = "Verify Your Email",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp
                ),
                color = textPrimary,
                textAlign = TextAlign.Center
            )
            
            // Message
            Text(
                text = "Verification email sent to",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 16.sp
                ),
                color = textSecondary,
                textAlign = TextAlign.Center
            )
            
            // Email address
            Text(
                text = email,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                ),
                color = textPrimary,
                textAlign = TextAlign.Center
            )
            
            // Instructions
            Text(
                text = "Please check your inbox and click the link to activate your account.",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = textSecondary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Back to Login Button
            Button(
                onClick = onBackToLogin,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(24.dp)),
                colors = ButtonDefaults.buttonColors(
                    containerColor = accentPrimary,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                Text(
                    text = "Back to Login",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                )
            }
            
            // Resend Email Button (optional)
            OutlinedButton(
                onClick = { /* TODO: Implement resend functionality */ },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .clip(RoundedCornerShape(24.dp)),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = accentPrimary
                ),
                shape = RoundedCornerShape(24.dp)
            ) {
                Text(
                    text = "Resend Email",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                )
            }
        }
    }
}
