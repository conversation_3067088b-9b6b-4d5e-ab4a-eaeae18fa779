package com.example.habits9.data.firestore

/**
 * Firestore data model for habit completions.
 * This is a simple POJO without Room annotations for Firestore serialization/deserialization.
 */
data class FirestoreCompletion(
    val id: String = "", // Firestore document ID
    val habitId: String = "", // Reference to habit document ID
    val timestamp: Long = 0L,
    val value: String? = null
) {
    // No-argument constructor required by Firestore
    constructor() : this(
        id = "",
        habitId = "",
        timestamp = 0L,
        value = null
    )
}
