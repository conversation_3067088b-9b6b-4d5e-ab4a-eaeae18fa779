{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-69:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8aaeb18dd8abb7cc16bdd65ee58c7a94\\transformed\\material3-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,305,418,543,645,747,866,1002,1123,1269,1354,1455,1546,1644,1756,1878,1984,2123,2260,2390,2549,2674,2789,2907,3023,3115,3214,3331,3463,3568,3673,3779,3917,4060,4170,4271,4347,4450,4550,4673,4761,4850,4955,5035,5119,5219,5319,5416,5514,5602,5706,5806,5908,6026,6106,6215", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "178,300,413,538,640,742,861,997,1118,1264,1349,1450,1541,1639,1751,1873,1979,2118,2255,2385,2544,2669,2784,2902,3018,3110,3209,3326,3458,3563,3668,3774,3912,4055,4165,4266,4342,4445,4545,4668,4756,4845,4950,5030,5114,5214,5314,5411,5509,5597,5701,5801,5903,6021,6101,6210,6308"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4362,4490,4612,4725,4850,4952,5054,5173,5309,5430,5576,5661,5762,5853,5951,6063,6185,6291,6430,6567,6697,6856,6981,7096,7214,7330,7422,7521,7638,7770,7875,7980,8086,8224,8367,8477,8578,8654,8757,8857,8980,9068,9157,9262,9342,9426,9526,9626,9723,9821,9909,10013,10113,10215,10333,10413,10522", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "4485,4607,4720,4845,4947,5049,5168,5304,5425,5571,5656,5757,5848,5946,6058,6180,6286,6425,6562,6692,6851,6976,7091,7209,7325,7417,7516,7633,7765,7870,7975,8081,8219,8362,8472,8573,8649,8752,8852,8975,9063,9152,9257,9337,9421,9521,9621,9718,9816,9904,10008,10108,10210,10328,10408,10517,10615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\364994c224617bb5658cf91cfb556286\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11624,11713", "endColumns": "88,93", "endOffsets": "11708,11802"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4106571b134d49d860c08c6ca142a08e\\transformed\\play-services-basement-18.4.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2259", "endColumns": "161", "endOffsets": "2416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\92faffc5ee3d50988e6ae9072fb227ba\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1059,1159,3589,3686,3787,4193,4274,10620,10712,10794,10875,10964,11036,11110,11186,11360,11441,11507", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "1154,1242,3681,3782,3873,4269,4357,10707,10789,10870,10959,11031,11105,11181,11254,11436,11502,11619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6806dc609573cce0b061254c5042724b\\transformed\\play-services-base-18.0.1\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1247,1356,1511,1644,1754,1900,2033,2153,2421,2587,2697,2846,2983,3127,3283,3347,3412", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "1351,1506,1639,1749,1895,2028,2148,2254,2582,2692,2841,2978,3122,3278,3342,3407,3488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b0062a5e1d214efb62fafece10c34c46\\transformed\\core-1.16.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "332,433,535,638,742,843,948,11259", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "428,530,633,737,838,943,1054,11355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e9549621c83de7ce5d44746d266914d0\\transformed\\credentials-1.2.0-rc01\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,214", "endColumns": "108,117", "endOffsets": "209,327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\07f408d290f2498181cf432fde42d716\\transformed\\browser-1.4.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3493,3878,3982,4090", "endColumns": "95,103,107,102", "endOffsets": "3584,3977,4085,4188"}}]}]}