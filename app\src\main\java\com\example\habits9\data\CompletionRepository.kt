package com.example.habits9.data

import android.util.Log
import com.example.habits9.data.firestore.FirestoreCompletion
import com.example.habits9.data.firestore.FirestoreConverters
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing habit completion data.
 * Serves as an abstraction layer between ViewModels and Firestore.
 */
@Singleton
class CompletionRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {

    companion object {
        private const val TAG = "CompletionRepository"
        private const val USERS_COLLECTION = "users"
        private const val COMPLETIONS_COLLECTION = "completions"
    }

    /**
     * Inserts a new completion record for the current user.
     */
    suspend fun insertCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot insert completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val firestoreCompletion = FirestoreConverters.completionToFirestore(
                completion,
                completion.habitId.toString() // Use habit ID as document reference
            )

            // Add the completion to Firestore (auto-generate document ID)
            val documentRef = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .add(firestoreCompletion)
                .await()

            Log.d(TAG, "Completion inserted with ID: ${documentRef.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting completion", e)
            throw e
        }
    }

    /**
     * Deletes a completion record for the current user.
     */
    suspend fun deleteCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .document(completion.id.toString())
                .delete()
                .await()

            Log.d(TAG, "Completion deleted: ${completion.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting completion ${completion.id}", e)
            throw e
        }
    }

    /**
     * Updates an existing completion record for the current user.
     */
    suspend fun updateCompletion(completion: Completion) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update completion")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val firestoreCompletion = FirestoreConverters.completionToFirestore(
                completion,
                completion.habitId.toString()
            )

            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .document(completion.id.toString())
                .set(firestoreCompletion)
                .await()

            Log.d(TAG, "Completion updated: ${completion.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating completion ${completion.id}", e)
            throw e
        }
    }

    /**
     * Gets all completions for a list of habit IDs within a specific date range.
     * Returns a Flow for reactive updates using real-time listeners.
     */
    fun getCompletionsForHabitsInRange(
        habitIds: List<Long>,
        startDate: Long,
        endDate: Long
    ): Flow<List<Completion>> = callbackFlow {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty completions list")
            trySend(emptyList())
            close()
            return@callbackFlow
        }

        val habitIdStrings = habitIds.map { it.toString() }

        val listener: ListenerRegistration = firestore
            .collection(USERS_COLLECTION)
            .document(userId)
            .collection(COMPLETIONS_COLLECTION)
            .whereIn("habitId", habitIdStrings)
            .whereGreaterThanOrEqualTo("timestamp", startDate)
            .whereLessThanOrEqualTo("timestamp", endDate)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error listening to completions", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val completions = snapshot.documents.mapNotNull { document ->
                        try {
                            val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                            firestoreCompletion?.let {
                                val completionWithId = it.copy(id = document.id)
                                // Convert habitId back to Long for Room compatibility
                                val roomHabitId = it.habitId.toLongOrNull() ?: 0L
                                FirestoreConverters.firestoreToCompletion(completionWithId, roomHabitId)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error converting document to completion: ${document.id}", e)
                            null
                        }
                    }
                    trySend(completions)
                } else {
                    trySend(emptyList())
                }
            }

        awaitClose { listener.remove() }
    }

    /**
     * Gets all completions for a specific habit using real-time listeners.
     */
    fun getCompletionsForHabit(habitId: Long): Flow<List<Completion>> = callbackFlow {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty completions list")
            trySend(emptyList())
            close()
            return@callbackFlow
        }

        val listener: ListenerRegistration = firestore
            .collection(USERS_COLLECTION)
            .document(userId)
            .collection(COMPLETIONS_COLLECTION)
            .whereEqualTo("habitId", habitId.toString())
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error listening to completions for habit $habitId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val completions = snapshot.documents.mapNotNull { document ->
                        try {
                            val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                            firestoreCompletion?.let {
                                val completionWithId = it.copy(id = document.id)
                                FirestoreConverters.firestoreToCompletion(completionWithId, habitId)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error converting document to completion: ${document.id}", e)
                            null
                        }
                    }
                    trySend(completions)
                } else {
                    trySend(emptyList())
                }
            }

        awaitClose { listener.remove() }
    }

    /**
     * Gets a specific completion for a habit on a specific date.
     * Returns null if no completion exists.
     */
    suspend fun getCompletionForHabitAndDate(habitId: Long, timestamp: Long): Completion? {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            return null
        }

        return try {
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .whereEqualTo("habitId", habitId.toString())
                .whereEqualTo("timestamp", timestamp)
                .get()
                .await()

            if (!snapshot.isEmpty) {
                val document = snapshot.documents.first()
                val firestoreCompletion = document.toObject(FirestoreCompletion::class.java)
                firestoreCompletion?.let {
                    val completionWithId = it.copy(id = document.id)
                    FirestoreConverters.firestoreToCompletion(completionWithId, habitId)
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting completion for habit $habitId at $timestamp", e)
            null
        }
    }

    /**
     * Deletes all completions for a specific habit.
     */
    suspend fun deleteAllCompletionsForHabit(habitId: Long) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete completions")
            throw IllegalStateException("User not authenticated")
        }

        try {
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(COMPLETIONS_COLLECTION)
                .whereEqualTo("habitId", habitId.toString())
                .get()
                .await()

            // Delete all matching documents
            val batch = firestore.batch()
            snapshot.documents.forEach { document ->
                batch.delete(document.reference)
            }
            batch.commit().await()

            Log.d(TAG, "All completions deleted for habit: $habitId")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting completions for habit $habitId", e)
            throw e
        }
    }
}